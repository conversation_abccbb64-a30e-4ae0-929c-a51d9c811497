<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> - Senior DevOps Engineer Resume</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', Arial, sans-serif;
            font-size: 7px;
            line-height: 1.2;
            color: #000;
            background: white;
            width: 210mm;
            height: 297mm;
            margin: 0 auto;
        }

        .resume {
            padding: 4mm 6mm;
            height: 100%;
        }

        .header {
            text-align: center;
            margin-bottom: 8px;
            border-bottom: 1px solid #000;
            padding-bottom: 6px;
        }

        .name {
            font-size: 18px;
            font-weight: 700;
            color: #000;
            margin-bottom: 4px;
            letter-spacing: 0.5px;
        }

        .title {
            font-size: 10px;
            color: #000;
            margin-bottom: 6px;
            font-weight: 500;
        }

        .contact-info {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 15px;
            font-size: 8px;
            color: #000;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .section {
            margin-bottom: 8px;
        }

        .section-title {
            font-size: 9px;
            font-weight: 700;
            color: #000;
            margin-bottom: 5px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-bottom: 1px solid #000;
            padding-bottom: 2px;
        }

        .summary {
            font-size: 7px;
            line-height: 1.3;
            margin-bottom: 8px;
            text-align: justify;
            color: #000;
        }

        .experience-item {
            margin-bottom: 8px;
            page-break-inside: avoid;
        }

        .job-title {
            font-size: 8px;
            font-weight: 700;
            color: #000;
            margin-bottom: 2px;
        }

        .company-date {
            font-size: 7px;
            color: #000;
            margin-bottom: 4px;
            font-style: italic;
        }

        .project-item {
            margin-bottom: 6px;
            padding: 4px 0;
            border-bottom: 1px dotted #000;
        }

        .project-title {
            font-size: 7px;
            font-weight: 600;
            color: #000;
            margin-bottom: 2px;
        }

        .tech-stack {
            font-size: 6px;
            color: #000;
            margin-bottom: 3px;
            font-style: italic;
        }
        
        .bullet-list {
            list-style-type: none;
            padding-left: 0;
        }

        .bullet-item {
            position: relative;
            padding-left: 8px;
            margin-bottom: 2px;
            font-size: 6px;
            line-height: 1.2;
            color: #000;
        }

        .bullet-item::before {
            content: "•";
            position: absolute;
            left: 0;
            color: #000;
            font-weight: bold;
        }

        .result-highlight {
            font-size: 6px;
            font-weight: 600;
            color: #000;
            margin-top: 3px;
            padding: 3px 0;
            border-top: 1px dotted #000;
        }

        .skills-section {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 8px;
            margin-bottom: 8px;
        }

        .skill-category {
            margin-bottom: 4px;
        }

        .skill-title {
            font-size: 6px;
            font-weight: 600;
            color: #000;
            margin-bottom: 2px;
            text-transform: uppercase;
            letter-spacing: 0.3px;
        }

        .skill-list {
            font-size: 5px;
            line-height: 1.2;
            color: #000;
        }
        
        .education-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 8px;
        }

        .education-item {
            margin-bottom: 4px;
        }

        .degree {
            font-size: 6px;
            font-weight: 600;
            color: #000;
            margin-bottom: 2px;
        }

        .university {
            font-size: 5px;
            color: #000;
            line-height: 1.2;
        }

        .highlight {
            font-weight: 700;
            color: #000;
        }

        .achievements-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
            margin-bottom: 8px;
        }

        .achievement-item {
            font-size: 5px;
            line-height: 1.2;
            color: #000;
        }

        .portfolio-section {
            margin-bottom: 6px;
        }

        .portfolio-links {
            font-size: 5px;
            line-height: 1.3;
            color: #000;
        }

        .portfolio-links a {
            color: #000;
            text-decoration: underline;
        }
        
        @media print {
            body {
                width: 210mm;
                height: 297mm;
                margin: 0;
                padding: 0;
            }
            
            .resume {
                padding: 6mm 8mm;
                height: 100%;
            }
            
            @page {
                size: A4;
                margin: 0;
            }
        }
    </style>
</head>
<body>
    <div class="resume">
        <div class="header">
            <h1 class="name">DILIP KUMAR</h1>
            <p class="title">Senior DevOps Engineer | Cloud Infrastructure Specialist | API Management Expert</p>
            <div class="contact-info">
                <div class="contact-item">📧 <EMAIL></div>
                <div class="contact-item">📱 +************</div>
                <div class="contact-item">🔗 github.com/Dilip-Devopos</div>
                <div class="contact-item">💼 linkedin.com/in/dilipkumarselvam</div>
                <div class="contact-item">📍 Bangalore, India</div>
            </div>
        </div>
        
        <div class="section">
            <h2 class="section-title">Professional Summary</h2>
            <p class="summary">
                Accomplished Senior DevOps Engineer with <span class="highlight">4+ years</span> of expertise in enterprise software development, cloud infrastructure automation, and scalable system architecture. Specialized in designing resilient CI/CD pipelines, containerized deployments, infrastructure as code, and API management solutions. Demonstrated proficiency in implementing DevSecOps practices, ensuring system reliability, scalability, and security with consistent integrity. Successfully delivered <span class="highlight">20+ production applications</span> with <span class="highlight">99.9% uptime</span>, reduced deployment time by <span class="highlight">80%</span>, and architected comprehensive monitoring ecosystems serving <span class="highlight">10,000+ users</span> with consistent performance and operational excellence.
            </p>
        </div>
        
        <div class="section">
            <h2 class="section-title">Professional Experience</h2>
            
            <div class="experience-item">
                <div class="job-title">Software Developer - DevOps Engineer</div>
                <div class="company-date">ZeOmega Infotech, Bangalore | Nov 2022 – Oct 2024</div>
                
                <div class="project-item">
                    <div class="project-title">Enterprise WSO2 API Management Migration & DevOps Implementation</div>
                    <div class="tech-stack">WSO2 API Manager • WSO2 Micro Integrator • Jenkins • Docker • Kubernetes • Puppet • Java</div>
                    <ul class="bullet-list">
                        <li class="bullet-item">Engineered CI/CD pipeline using Jenkins with Dependency Check tool for JAR vulnerability scanning ensuring security compliance and scalable deployment</li>
                        <li class="bullet-item">Designed automated workflows using Puppet for environment orchestration and WSO2 API Manager deployment ensuring scalable infrastructure</li>
                        <li class="bullet-item">Migrated 50+ enterprise APIs from Windows to Kubernetes architecture with zero downtime and enhanced scalability</li>
                        <li class="bullet-item">Implemented API security framework with OAuth2, rate limiting, key management ensuring system integrity and consistent performance</li>
                    </ul>
                    <div class="result-highlight">Results: 60% faster deployments, 99.8% availability, 65% reduction in manual setup time, enhanced scalable architecture</div>
                </div>
            </div>
            
            <div class="experience-item">
                <div class="job-title">Senior DevOps Engineer (Freelance Projects)</div>
                <div class="company-date">Independent Projects | 2024 - Present</div>
                
                <div class="project-item">
                    <div class="project-title">Enterprise ReactJS CI/CD Pipeline with Advanced Security</div>
                    <div class="tech-stack">Jenkins • Docker • AWS EC2 • SonarQube • OWASP • Trivy • Prometheus • Grafana</div>
                    <ul class="bullet-list">
                        <li class="bullet-item">Architected DevSecOps pipeline supporting 5,000+ users with Jenkins Multibranch strategy ensuring scalable deployment</li>
                        <li class="bullet-item">Embedded OWASP and Trivy security scanning for vulnerability assessment with consistent integrity</li>
                        <li class="bullet-item">Integrated SonarQube for code quality analysis ensuring consistent standards and scalable development</li>
                        <li class="bullet-item">Deployed Prometheus, Grafana monitoring ecosystem ensuring reliability and proactive incident response</li>
                    </ul>
                    <div class="result-highlight">Results: 99.9% uptime, 95% security incident reduction, 2 hours to 15 minutes deployment acceleration, enhanced scalability</div>
                </div>

                <div class="project-item">
                    <div class="project-title">Kubernetes-Native Application Deployment on AWS EKS</div>
                    <div class="tech-stack">AWS EKS • Terraform • Kubernetes • Jenkins • Prometheus • Grafana • Fluent Bit</div>
                    <ul class="bullet-list">
                        <li class="bullet-item">Designed AWS infrastructure using Terraform implementing VPC, EKS cluster with GitOps ensuring scalable deployment</li>
                        <li class="bullet-item">Executed CI/CD pipeline with Docker builds, Kubernetes deployments via Jenkins ensuring consistent delivery</li>
                        <li class="bullet-item">Orchestrated containerized applications with autoscaling, load balancing ensuring scalable performance</li>
                        <li class="bullet-item">Implemented cost optimization through right-sizing and auto-scaling ensuring efficient resource utilization</li>
                    </ul>
                    <div class="result-highlight">Results: 40% cost optimization, 10+ microservices managed, 35% resource cost reduction, enhanced scalability</div>
                </div>

                <div class="project-item">
                    <div class="project-title">AWS-Native CI/CD Pipeline with Advanced Automation</div>
                    <div class="tech-stack">AWS CodePipeline • CodeBuild • CodeDeploy • ECR • EKS • CloudWatch • SNS</div>
                    <ul class="bullet-list">
                        <li class="bullet-item">Constructed AWS-native CI/CD using CodePipeline, CodeBuild, CodeDeploy ensuring scalable delivery</li>
                        <li class="bullet-item">Automated Docker image management with ECR integration ensuring consistent deployments</li>
                        <li class="bullet-item">Implemented Trivy vulnerability scanning with SNS notifications ensuring system integrity</li>
                        <li class="bullet-item">Established CloudWatch monitoring with custom metrics ensuring operational excellence</li>
                    </ul>
                    <div class="result-highlight">Results: 90% manual task reduction, 100+ deployments with 99.5% success rate, enhanced scalable automation</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">Technical Skills</h2>
            <div class="skills-section">
                <div class="skill-category">
                    <div class="skill-title">Cloud Platforms</div>
                    <div class="skill-list">AWS (EC2, EKS, ECR, CodePipeline, CodeBuild, CodeDeploy, CloudWatch, SNS, VPC, IAM, CloudFormation)</div>
                </div>

                <div class="skill-category">
                    <div class="skill-title">Containers & Orchestration</div>
                    <div class="skill-list">Docker, Kubernetes, Helm, Rancher, Docker Compose</div>
                </div>

                <div class="skill-category">
                    <div class="skill-title">CI/CD & DevOps</div>
                    <div class="skill-list">Jenkins, AWS CodePipeline, GitHub Actions, GitLab CI, ArgoCD</div>
                </div>

                <div class="skill-category">
                    <div class="skill-title">Infrastructure as Code</div>
                    <div class="skill-list">Terraform, CloudFormation, Ansible, Puppet</div>
                </div>

                <div class="skill-category">
                    <div class="skill-title">Monitoring & Observability</div>
                    <div class="skill-list">Prometheus, Grafana, AlertManager, Fluent Bit, Grafana Loki, CloudWatch</div>
                </div>

                <div class="skill-category">
                    <div class="skill-title">Security & Compliance</div>
                    <div class="skill-list">SonarQube, OWASP Dependency-Check, Trivy, Snyk, DevSecOps</div>
                </div>

                <div class="skill-category">
                    <div class="skill-title">API Management</div>
                    <div class="skill-list">WSO2 API Manager, WSO2 Micro Integrator, REST APIs, OAuth2, Rate Limiting</div>
                </div>

                <div class="skill-category">
                    <div class="skill-title">Programming & Scripting</div>
                    <div class="skill-list">Python, Java, Bash, SQL, YAML, JSON, ReactJS, JavaScript, HTML/CSS</div>
                </div>

                <div class="skill-category">
                    <div class="skill-title">Database Technologies</div>
                    <div class="skill-list">MySQL, PostgreSQL, MongoDB, Redis, SQL Server</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">Education & Certifications</h2>
            <div class="education-section">
                <div class="education-item">
                    <div class="degree">Master of Computer Application (MCA)</div>
                    <div class="university">
                        Madurai Kamaraj University<br>
                        Aug 2022 – May 2024 | CGPA: 7.2/10<br>
                        Madurai, Tamil Nadu
                    </div>
                </div>

                <div class="education-item">
                    <div class="degree">Professional Certifications</div>
                    <div class="university">
                        • AWS Solutions Architect (In Progress)<br>
                        • Kubernetes Administrator (In Progress)<br>
                        • Jenkins Certified Engineer<br>
                        • WSO2 API Manager Developer<br>
                        • Docker Associate (Planned)
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">Key Achievements & Portfolio</h2>
            <div class="achievements-grid">
                <div class="achievement-item">
                    <strong>Cost Optimization:</strong> 40% AWS cost reduction through scalable infrastructure<br>
                    <strong>Security:</strong> 95% security incident reduction ensuring system integrity<br>
                    <strong>Performance:</strong> 80% deployment acceleration ensuring consistent delivery
                </div>

                <div class="achievement-item">
                    <strong>Reliability:</strong> 99.9% uptime with scalable infrastructure<br>
                    <strong>Automation:</strong> 90% manual task elimination ensuring operational integrity<br>
                    <strong>Migration:</strong> 50+ APIs migrated with enhanced scalability
                </div>

                <div class="achievement-item">
                    <strong>Portfolio:</strong> ReactJS CI/CD: <a href="http://***********/">Live Demo</a><br>
                    <strong>EKS App:</strong> <a href="http://a663570b339364482a86a9e6f3445b69-1151892002.us-west-2.elb.amazonaws.com/">Kubernetes</a><br>
                    <strong>AWS Pipeline:</strong> <a href="http://abb59c4b452064c768a9c0975c9ced80-1446271263.us-east-1.elb.amazonaws.com">CodePipeline</a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
