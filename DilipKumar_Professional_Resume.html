<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> - Senior DevOps Engineer Resume</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', Arial, sans-serif;
            font-size: 9px;
            line-height: 1.4;
            color: #000;
            background: white;
            width: 210mm;
            height: 297mm;
            margin: 0 auto;
        }
        
        .resume {
            padding: 6mm 8mm;
            height: 100%;
        }
        
        .header {
            text-align: center;
            margin-bottom: 15px;
            border-bottom: 2px solid #000;
            padding-bottom: 12px;
        }
        
        .name {
            font-size: 24px;
            font-weight: 700;
            color: #000;
            margin-bottom: 8px;
            letter-spacing: 1px;
        }
        
        .title {
            font-size: 14px;
            color: #000;
            margin-bottom: 10px;
            font-weight: 500;
        }
        
        .contact-info {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 25px;
            font-size: 10px;
            color: #000;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .section {
            margin-bottom: 18px;
        }
        
        .section-title {
            font-size: 13px;
            font-weight: 700;
            color: #000;
            margin-bottom: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
            border-bottom: 2px solid #000;
            padding-bottom: 5px;
        }
        
        .summary {
            font-size: 10px;
            line-height: 1.5;
            margin-bottom: 18px;
            text-align: justify;
            color: #000;
        }
        
        .experience-item {
            margin-bottom: 15px;
            page-break-inside: avoid;
        }
        
        .job-title {
            font-size: 12px;
            font-weight: 700;
            color: #000;
            margin-bottom: 5px;
        }
        
        .company-date {
            font-size: 10px;
            color: #000;
            margin-bottom: 10px;
            font-style: italic;
        }
        
        .project-item {
            margin-bottom: 12px;
            padding: 10px 0;
            border-bottom: 1px solid #000;
        }
        
        .project-title {
            font-size: 11px;
            font-weight: 600;
            color: #000;
            margin-bottom: 5px;
        }
        
        .tech-stack {
            font-size: 9px;
            color: #000;
            margin-bottom: 8px;
            font-style: italic;
        }
        
        .bullet-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .bullet-item {
            position: relative;
            padding-left: 15px;
            margin-bottom: 5px;
            font-size: 9px;
            line-height: 1.4;
            color: #000;
        }
        
        .bullet-item::before {
            content: "•";
            position: absolute;
            left: 0;
            color: #000;
            font-weight: bold;
        }
        
        .result-highlight {
            font-size: 9px;
            font-weight: 600;
            color: #000;
            margin-top: 6px;
            padding: 6px 0;
            border-top: 1px dotted #000;
        }
        
        .skills-section {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 18px;
        }
        
        .skill-category {
            margin-bottom: 10px;
        }
        
        .skill-title {
            font-size: 10px;
            font-weight: 600;
            color: #000;
            margin-bottom: 5px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .skill-list {
            font-size: 8px;
            line-height: 1.4;
            color: #000;
        }
        
        .education-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            margin-bottom: 18px;
        }
        
        .education-item {
            margin-bottom: 10px;
        }
        
        .degree {
            font-size: 10px;
            font-weight: 600;
            color: #000;
            margin-bottom: 4px;
        }
        
        .university {
            font-size: 8px;
            color: #000;
            line-height: 1.4;
        }
        
        .highlight {
            font-weight: 700;
            color: #000;
        }
        
        .achievements-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 18px;
        }
        
        .achievement-item {
            font-size: 9px;
            line-height: 1.4;
            color: #000;
        }
        
        .portfolio-section {
            margin-bottom: 15px;
        }
        
        .portfolio-links {
            font-size: 8px;
            line-height: 1.5;
            color: #000;
        }
        
        .portfolio-links a {
            color: #000;
            text-decoration: underline;
        }
        
        @media print {
            body {
                width: 210mm;
                height: 297mm;
                margin: 0;
                padding: 0;
            }
            
            .resume {
                padding: 6mm 8mm;
                height: 100%;
            }
            
            @page {
                size: A4;
                margin: 0;
            }
        }
    </style>
</head>
<body>
    <div class="resume">
        <div class="header">
            <h1 class="name">DILIP KUMAR</h1>
            <p class="title">Senior DevOps Engineer | Cloud Infrastructure Specialist | API Management Expert</p>
            <div class="contact-info">
                <div class="contact-item">📧 <EMAIL></div>
                <div class="contact-item">📱 +************</div>
                <div class="contact-item">🔗 github.com/Dilip-Devopos</div>
                <div class="contact-item">💼 linkedin.com/in/dilipkumarselvam</div>
                <div class="contact-item">📍 Bangalore, India</div>
            </div>
        </div>
        
        <div class="section">
            <h2 class="section-title">Professional Summary</h2>
            <p class="summary">
                Accomplished Senior DevOps Engineer with <span class="highlight">4+ years</span> of expertise in enterprise software development, cloud infrastructure automation, and scalable system architecture. Specialized in designing resilient CI/CD pipelines, containerized deployments, infrastructure as code, and API management solutions. Demonstrated proficiency in implementing DevSecOps practices, ensuring system reliability, scalability, and security with consistent integrity. Successfully delivered <span class="highlight">20+ production applications</span> with <span class="highlight">99.9% uptime</span>, reduced deployment time by <span class="highlight">80%</span>, and architected comprehensive monitoring ecosystems serving <span class="highlight">10,000+ users</span> with consistent performance and operational excellence.
            </p>
        </div>
        
        <div class="section">
            <h2 class="section-title">Professional Experience</h2>
            
            <div class="experience-item">
                <div class="job-title">Software Developer - DevOps Engineer</div>
                <div class="company-date">ZeOmega Infotech, Bangalore | Nov 2022 – Oct 2024</div>
                
                <div class="project-item">
                    <div class="project-title">Enterprise WSO2 API Management Migration & DevOps Implementation</div>
                    <div class="tech-stack">WSO2 API Manager • WSO2 Micro Integrator • Jenkins • Docker • Kubernetes • Puppet • Java</div>
                    <ul class="bullet-list">
                        <li class="bullet-item">Engineered robust CI/CD pipeline using Jenkins with integrated Dependency Check tool for comprehensive JAR file vulnerability scanning and security compliance</li>
                        <li class="bullet-item">Designed automated workflows using Puppet for environment orchestration, configuration management, and seamless WSO2 API Manager deployment ensuring scalable infrastructure</li>
                        <li class="bullet-item">Spearheaded complete migration of 50+ enterprise APIs from legacy Windows servers to cloud-native Kubernetes architecture with zero downtime and enhanced scalability</li>
                        <li class="bullet-item">Implemented comprehensive API security framework incorporating rate limiting, OAuth2 authentication flows, key management, and access control policies ensuring system integrity</li>
                        <li class="bullet-item">Orchestrated automated security patch management for WSO2 components using built-in executable tools ensuring consistent system integrity and compliance</li>
                        <li class="bullet-item">Established monitoring and alerting systems with real-time performance metrics, ensuring consistent API availability and proactive issue resolution</li>
                    </ul>
                    <div class="result-highlight">Results: Successfully migrated 50+ APIs with 60% faster deployments, 99.8% availability, 65% reduction in manual setup time, and enhanced security posture with scalable architecture</div>
                </div>
            </div>
            
            <div class="experience-item">
                <div class="job-title">Senior DevOps Engineer (Freelance Projects)</div>
                <div class="company-date">Independent Projects | 2024 - Present</div>
                
                <div class="project-item">
                    <div class="project-title">Enterprise ReactJS CI/CD Pipeline with Advanced Security</div>
                    <div class="tech-stack">Jenkins • Docker • AWS EC2 • SonarQube • OWASP • Trivy • Prometheus • Grafana</div>
                    <ul class="bullet-list">
                        <li class="bullet-item">Architected enterprise-grade DevSecOps pipeline supporting 5,000+ concurrent users with multi-branch CI/CD strategy using Jenkins Multibranch Pipelines ensuring scalable deployment</li>
                        <li class="bullet-item">Embedded advanced security scanning integrating OWASP Dependency-Check and Trivy for comprehensive vulnerability assessment and threat mitigation with consistent integrity</li>
                        <li class="bullet-item">Integrated SonarQube for automated code quality analysis, ensuring consistent code standards, maintainability, and technical debt reduction across development lifecycle</li>
                        <li class="bullet-item">Deployed comprehensive monitoring ecosystem using Prometheus for metrics collection, Grafana for visualization, and AlertManager for proactive incident response ensuring reliability</li>
                        <li class="bullet-item">Configured Docker Hub integration with segregated repositories for development and production environments ensuring secure container image management and scalable deployments</li>
                        <li class="bullet-item">Implemented automated deployment workflows with rollback capabilities, ensuring system reliability and minimizing service disruption with consistent performance</li>
                    </ul>
                    <div class="result-highlight">Results: Achieved 99.9% uptime, 95% reduction in security incidents, accelerated deployment from 2 hours to 15 minutes, enhanced system scalability and operational integrity</div>
                </div>

                <div class="project-item">
                    <div class="project-title">Kubernetes-Native Application Deployment on AWS EKS</div>
                    <div class="tech-stack">AWS EKS • Terraform • Kubernetes • Jenkins • Prometheus • Grafana • Fluent Bit</div>
                    <ul class="bullet-list">
                        <li class="bullet-item">Designed scalable AWS infrastructure using Terraform for Infrastructure as Code, implementing VPC, subnets, security groups, and EKS cluster with GitOps workflow ensuring consistent deployment</li>
                        <li class="bullet-item">Executed end-to-end CI/CD pipeline with automated Docker builds, Kubernetes deployments via Jenkins, and Helm charts for consistent application delivery and scalable architecture</li>
                        <li class="bullet-item">Orchestrated containerized applications with horizontal pod autoscaling, load balancing, and health monitoring ensuring high availability, resilience, and scalable performance</li>
                        <li class="bullet-item">Instituted comprehensive observability using Prometheus for metrics collection, Grafana for visualization, and Fluent Bit for centralized logging ensuring operational integrity</li>
                        <li class="bullet-item">Implemented cost optimization strategies through right-sizing, auto-scaling configurations, and resource management policies ensuring efficient and scalable resource utilization</li>
                    </ul>
                    <div class="result-highlight">Results: Realized 40% cost optimization, managed 10+ microservices with 35% resource cost reduction, enhanced system scalability, reliability, and operational integrity</div>
                </div>

                <div class="project-item">
                    <div class="project-title">AWS-Native CI/CD Pipeline with Advanced Automation</div>
                    <div class="tech-stack">AWS CodePipeline • CodeBuild • CodeDeploy • ECR • EKS • CloudWatch • SNS</div>
                    <ul class="bullet-list">
                        <li class="bullet-item">Constructed AWS-native CI/CD solution utilizing CodePipeline, CodeBuild, and CodeDeploy for seamless integration and automated deployment workflows ensuring scalable delivery</li>
                        <li class="bullet-item">Devised automated Docker image management with ECR integration, sophisticated tagging strategies, and version control for consistent deployments and scalable architecture</li>
                        <li class="bullet-item">Pioneered vulnerability scanning pipeline leveraging Trivy with automated security reporting and SNS notification systems for proactive threat detection and system integrity</li>
                        <li class="bullet-item">Implemented zero-downtime deployment strategies with automated rollback capabilities ensuring system integrity, service continuity, and consistent performance</li>
                        <li class="bullet-item">Established comprehensive monitoring using CloudWatch with custom metrics, dashboards, and alerting for operational excellence and scalable observability</li>
                    </ul>
                    <div class="result-highlight">Results: Attained 90% reduction in manual deployment tasks, processed 100+ automated deployments with 99.5% success rate, enhanced operational efficiency and scalable automation</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">Technical Skills</h2>
            <div class="skills-section">
                <div class="skill-category">
                    <div class="skill-title">Cloud Platforms</div>
                    <div class="skill-list">AWS (EC2, EKS, ECR, CodePipeline, CodeBuild, CodeDeploy, CloudWatch, SNS, VPC, IAM, CloudFormation)</div>
                </div>

                <div class="skill-category">
                    <div class="skill-title">Containers & Orchestration</div>
                    <div class="skill-list">Docker, Kubernetes, Helm, Rancher, Docker Compose</div>
                </div>

                <div class="skill-category">
                    <div class="skill-title">CI/CD & DevOps</div>
                    <div class="skill-list">Jenkins, AWS CodePipeline, GitHub Actions, GitLab CI, ArgoCD</div>
                </div>

                <div class="skill-category">
                    <div class="skill-title">Infrastructure as Code</div>
                    <div class="skill-list">Terraform, CloudFormation, Ansible, Puppet</div>
                </div>

                <div class="skill-category">
                    <div class="skill-title">Monitoring & Observability</div>
                    <div class="skill-list">Prometheus, Grafana, AlertManager, Fluent Bit, Grafana Loki, CloudWatch</div>
                </div>

                <div class="skill-category">
                    <div class="skill-title">Security & Compliance</div>
                    <div class="skill-list">SonarQube, OWASP Dependency-Check, Trivy, Snyk, DevSecOps</div>
                </div>

                <div class="skill-category">
                    <div class="skill-title">API Management</div>
                    <div class="skill-list">WSO2 API Manager, WSO2 Micro Integrator, REST APIs, OAuth2, Rate Limiting</div>
                </div>

                <div class="skill-category">
                    <div class="skill-title">Programming & Scripting</div>
                    <div class="skill-list">Python, Java, Bash, SQL, YAML, JSON, ReactJS, JavaScript, HTML/CSS</div>
                </div>

                <div class="skill-category">
                    <div class="skill-title">Database Technologies</div>
                    <div class="skill-list">MySQL, PostgreSQL, MongoDB, Redis, SQL Server</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">Education & Certifications</h2>
            <div class="education-section">
                <div class="education-item">
                    <div class="degree">Master of Computer Application (MCA)</div>
                    <div class="university">
                        Madurai Kamaraj University<br>
                        Aug 2022 – May 2024 | CGPA: 7.2/10<br>
                        Madurai, Tamil Nadu
                    </div>
                </div>

                <div class="education-item">
                    <div class="degree">Professional Certifications</div>
                    <div class="university">
                        • AWS Solutions Architect (In Progress)<br>
                        • Kubernetes Administrator (In Progress)<br>
                        • Jenkins Certified Engineer<br>
                        • WSO2 API Manager Developer<br>
                        • Docker Associate (Planned)
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">Key Achievements & Portfolio</h2>
            <div class="achievements-grid">
                <div class="achievement-item">
                    <strong>Cost Optimization:</strong> Achieved 40% AWS cost reduction through strategic right-sizing, auto-scaling, and resource optimization ensuring scalable infrastructure<br><br>
                    <strong>Security Enhancement:</strong> Reduced security incidents by 95% through automated vulnerability scanning and DevSecOps practices ensuring system integrity<br><br>
                    <strong>Performance Improvement:</strong> Accelerated deployment processes by 80% from 2 hours to 15 minutes through automation ensuring consistent delivery
                </div>

                <div class="achievement-item">
                    <strong>System Reliability:</strong> Maintained 99.9% uptime across all production applications with scalable infrastructure and consistent performance<br><br>
                    <strong>Process Automation:</strong> Eliminated 90% of manual deployment tasks through comprehensive CI/CD pipeline automation ensuring operational integrity<br><br>
                    <strong>Migration Success:</strong> Successfully migrated 50+ enterprise APIs to cloud-native architecture with zero data loss and enhanced scalability
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">Live Portfolio Demonstrations</h2>
            <div class="portfolio-section">
                <div class="portfolio-links">
                    <strong>ReactJS CI/CD Pipeline:</strong> <a href="http://***********/">http://***********/</a> - Enterprise DevSecOps pipeline with comprehensive monitoring and scalable architecture<br>
                    <strong>EKS Kubernetes Application:</strong> <a href="http://a663570b339364482a86a9e6f3445b69-1151892002.us-west-2.elb.amazonaws.com/">Kubernetes Deployment</a> - Scalable containerized application on AWS EKS with operational integrity<br>
                    <strong>AWS Native Pipeline:</strong> <a href="http://abb59c4b452064c768a9c0975c9ced80-1446271263.us-east-1.elb.amazonaws.com">AWS CodePipeline</a> - Fully automated AWS-native CI/CD solution with consistent delivery<br>
                    <strong>Monitoring Dashboards:</strong> Prometheus, Grafana, and SonarQube dashboards demonstrating comprehensive observability, code quality metrics, and scalable monitoring
                </div>
            </div>
        </div>
    </div>
</body>
</html>
