<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> - Senior DevOps Engineer Resume</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', Arial, sans-serif;
            font-size: 9px;
            line-height: 1.4;
            color: #000;
            background: white;
            width: 210mm;
            height: 297mm;
            margin: 0 auto;
        }

        .resume {
            padding: 6mm 8mm;
            height: 100%;
        }

        .header {
            text-align: center;
            margin-bottom: 12px;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
        }

        .name {
            font-size: 22px;
            font-weight: 700;
            color: #000;
            margin-bottom: 6px;
            letter-spacing: 1px;
        }

        .title {
            font-size: 13px;
            color: #000;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .contact-info {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 20px;
            font-size: 10px;
            color: #000;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .single-column {
            width: 100%;
        }
        
        .section {
            margin-bottom: 15px;
        }

        .section-title {
            font-size: 12px;
            font-weight: 700;
            color: #000;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            border-bottom: 2px solid #000;
            padding-bottom: 4px;
        }

        .summary {
            font-size: 10px;
            line-height: 1.5;
            margin-bottom: 15px;
            text-align: justify;
            color: #000;
        }

        .experience-item {
            margin-bottom: 12px;
            page-break-inside: avoid;
        }

        .job-title {
            font-size: 11px;
            font-weight: 700;
            color: #000;
            margin-bottom: 4px;
        }

        .company-date {
            font-size: 9px;
            color: #000;
            margin-bottom: 8px;
            font-style: italic;
        }

        .project-item {
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #000;
        }

        .project-title {
            font-size: 10px;
            font-weight: 600;
            color: #000;
            margin-bottom: 4px;
        }

        .tech-stack {
            font-size: 8px;
            color: #000;
            margin-bottom: 6px;
            font-style: italic;
        }

        .bullet-list {
            list-style-type: none;
            padding-left: 0;
        }

        .bullet-item {
            position: relative;
            padding-left: 12px;
            margin-bottom: 4px;
            font-size: 9px;
            line-height: 1.4;
            color: #000;
        }

        .bullet-item::before {
            content: "•";
            position: absolute;
            left: 0;
            color: #000;
            font-weight: bold;
        }

        .result-highlight {
            font-size: 9px;
            font-weight: 600;
            color: #000;
            margin-top: 4px;
            padding: 4px 0;
            border-top: 1px dotted #000;
        }
        
        .skills-section {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-bottom: 15px;
        }

        .skill-category {
            margin-bottom: 8px;
        }

        .skill-title {
            font-size: 9px;
            font-weight: 600;
            color: #000;
            margin-bottom: 4px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .skill-list {
            font-size: 8px;
            line-height: 1.4;
            color: #000;
        }

        .education-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 15px;
        }

        .education-item {
            margin-bottom: 8px;
        }

        .degree {
            font-size: 9px;
            font-weight: 600;
            color: #000;
            margin-bottom: 3px;
        }

        .university {
            font-size: 8px;
            color: #000;
            line-height: 1.4;
        }

        .highlight {
            font-weight: 700;
            color: #000;
        }

        .achievements-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 15px;
        }

        .achievement-item {
            font-size: 8px;
            line-height: 1.4;
            color: #000;
        }

        .portfolio-section {
            margin-bottom: 10px;
        }

        .portfolio-links {
            font-size: 8px;
            line-height: 1.5;
            color: #000;
        }

        .portfolio-links a {
            color: #000;
            text-decoration: underline;
        }
        
        @media print {
            body {
                width: 210mm;
                height: 297mm;
                margin: 0;
                padding: 0;
            }
            
            .resume {
                padding: 8mm 10mm;
                height: 100%;
            }
            
            @page {
                size: A4;
                margin: 0;
            }
        }
    </style>
</head>
<body>
    <div class="resume">
        <div class="header">
            <h1 class="name">DILIP KUMAR</h1>
            <p class="title">Senior DevOps Engineer | Cloud Infrastructure Specialist | API Management Expert</p>
            <div class="contact-info">
                <div class="contact-item">📧 <EMAIL></div>
                <div class="contact-item">📱 +************</div>
                <div class="contact-item">🔗 github.com/Dilip-Devopos</div>
                <div class="contact-item">💼 linkedin.com/in/dilipkumarselvam</div>
                <div class="contact-item">📍 Bangalore, India</div>
            </div>
        </div>
        
        <div class="single-column">
            <div class="section">
                <h2 class="section-title">Professional Summary</h2>
                <p class="summary">
                    Accomplished Senior DevOps Engineer with <span class="highlight">4+ years</span> of expertise in enterprise software development, cloud infrastructure automation, and scalable system architecture. Specialized in designing resilient CI/CD pipelines, containerized deployments, infrastructure as code, and API management solutions. Demonstrated proficiency in implementing DevSecOps practices, ensuring system reliability, scalability, and security. Successfully delivered <span class="highlight">20+ production applications</span> with <span class="highlight">99.9% uptime</span>, reduced deployment time by <span class="highlight">80%</span>, and architected comprehensive monitoring ecosystems serving <span class="highlight">10,000+ users</span> with consistent performance and integrity.
                </p>
            </div>
                
            <div class="section">
                <h2 class="section-title">Professional Experience</h2>

                <div class="experience-item">
                    <div class="job-title">Software Developer - DevOps Engineer</div>
                    <div class="company-date">ZeOmega Infotech, Bangalore | Nov 2022 – Oct 2024</div>

                    <div class="project-item">
                        <div class="project-title">Enterprise WSO2 API Management Migration & DevOps Implementation</div>
                        <div class="tech-stack">WSO2 API Manager • WSO2 Micro Integrator • Jenkins • Docker • Kubernetes • Puppet • Java</div>
                        <ul class="bullet-list">
                            <li class="bullet-item">Engineered robust CI/CD pipeline using Jenkins with integrated Dependency Check tool for comprehensive JAR file vulnerability scanning and security compliance</li>
                            <li class="bullet-item">Designed automated workflows using Puppet for environment orchestration, configuration management, and seamless WSO2 API Manager deployment across multiple environments</li>
                            <li class="bullet-item">Spearheaded complete migration of 50+ enterprise APIs from legacy Windows servers to cloud-native Kubernetes architecture with zero downtime and enhanced scalability</li>
                            <li class="bullet-item">Implemented comprehensive API security framework incorporating rate limiting, OAuth2 authentication flows, key management, and access control policies</li>
                            <li class="bullet-item">Orchestrated automated security patch management for WSO2 components using built-in executable tools ensuring system integrity and compliance</li>
                            <li class="bullet-item">Established monitoring and alerting systems with real-time performance metrics, ensuring consistent API availability and proactive issue resolution</li>
                        </ul>
                        <div class="result-highlight">Results: Successfully migrated 50+ APIs with 60% faster deployments, 99.8% availability, 65% reduction in manual setup time, and enhanced security posture</div>
                    </div>
                </div>
                    
                <div class="experience-item">
                    <div class="job-title">Senior DevOps Engineer (Freelance Projects)</div>
                    <div class="company-date">Independent Projects | 2024 - Present</div>

                    <div class="project-item">
                        <div class="project-title">Enterprise ReactJS CI/CD Pipeline with Advanced Security</div>
                        <div class="tech-stack">Jenkins • Docker • AWS EC2 • SonarQube • OWASP • Trivy • Prometheus • Grafana</div>
                        <ul class="bullet-list">
                            <li class="bullet-item">Architected enterprise-grade DevSecOps pipeline supporting 5,000+ concurrent users with multi-branch CI/CD strategy using Jenkins Multibranch Pipelines</li>
                            <li class="bullet-item">Embedded advanced security scanning integrating OWASP Dependency-Check and Trivy for comprehensive vulnerability assessment and threat mitigation</li>
                            <li class="bullet-item">Integrated SonarQube for automated code quality analysis, ensuring consistent code standards, maintainability, and technical debt reduction</li>
                            <li class="bullet-item">Deployed comprehensive monitoring ecosystem using Prometheus for metrics collection, Grafana for visualization, and AlertManager for proactive incident response</li>
                            <li class="bullet-item">Configured Docker Hub integration with segregated repositories for development and production environments ensuring secure container image management</li>
                            <li class="bullet-item">Implemented automated deployment workflows with rollback capabilities, ensuring system reliability and minimizing service disruption</li>
                        </ul>
                        <div class="result-highlight">Results: Achieved 99.9% uptime, 95% reduction in security incidents, accelerated deployment from 2 hours to 15 minutes, enhanced system scalability</div>
                    </div>
                        
                    <div class="project-item">
                        <div class="project-title">Kubernetes-Native Application Deployment on AWS EKS</div>
                        <div class="tech-stack">AWS EKS • Terraform • Kubernetes • Jenkins • Prometheus • Grafana • Fluent Bit</div>
                        <ul class="bullet-list">
                            <li class="bullet-item">Designed scalable AWS infrastructure using Terraform for Infrastructure as Code, implementing VPC, subnets, security groups, and EKS cluster with GitOps workflow</li>
                            <li class="bullet-item">Executed end-to-end CI/CD pipeline with automated Docker builds, Kubernetes deployments via Jenkins, and Helm charts for consistent application delivery</li>
                            <li class="bullet-item">Orchestrated containerized applications with horizontal pod autoscaling, load balancing, and health monitoring ensuring high availability and resilience</li>
                            <li class="bullet-item">Instituted comprehensive observability using Prometheus for metrics collection, Grafana for visualization, and Fluent Bit for centralized logging and monitoring</li>
                            <li class="bullet-item">Implemented cost optimization strategies through right-sizing, auto-scaling configurations, and resource management policies</li>
                        </ul>
                        <div class="result-highlight">Results: Realized 40% cost optimization, managed 10+ microservices with 35% resource cost reduction, enhanced system scalability and reliability</div>
                    </div>

                    <div class="project-item">
                        <div class="project-title">AWS-Native CI/CD Pipeline with Advanced Automation</div>
                        <div class="tech-stack">AWS CodePipeline • CodeBuild • CodeDeploy • ECR • EKS • CloudWatch • SNS</div>
                        <ul class="bullet-list">
                            <li class="bullet-item">Constructed AWS-native CI/CD solution utilizing CodePipeline, CodeBuild, and CodeDeploy for seamless integration and automated deployment workflows</li>
                            <li class="bullet-item">Devised automated Docker image management with ECR integration, sophisticated tagging strategies, and version control for consistent deployments</li>
                            <li class="bullet-item">Pioneered vulnerability scanning pipeline leveraging Trivy with automated security reporting and SNS notification systems for proactive threat detection</li>
                            <li class="bullet-item">Implemented zero-downtime deployment strategies with automated rollback capabilities ensuring system integrity and service continuity</li>
                            <li class="bullet-item">Established comprehensive monitoring using CloudWatch with custom metrics, dashboards, and alerting for operational excellence</li>
                        </ul>
                        <div class="result-highlight">Results: Attained 90% reduction in manual deployment tasks, processed 100+ automated deployments with 99.5% success rate, enhanced operational efficiency</div>
                    </div>
                </div>
            </div>
            </div>
            
            <div class="right-column">
                <div class="section">
                    <h2 class="section-title">Technical Skills</h2>
                    <div class="skills-grid">
                        <div class="skill-category">
                            <div class="skill-title">Cloud Platforms</div>
                            <div class="skill-list">AWS (EC2, EKS, ECR, CodePipeline, CodeBuild, CodeDeploy, CloudWatch, SNS, VPC, IAM, CloudFormation)</div>
                        </div>
                        
                        <div class="skill-category">
                            <div class="skill-title">Containers & Orchestration</div>
                            <div class="skill-list">Docker, Kubernetes, Helm, Rancher, Docker Compose</div>
                        </div>
                        
                        <div class="skill-category">
                            <div class="skill-title">CI/CD & DevOps</div>
                            <div class="skill-list">Jenkins, AWS CodePipeline, GitHub Actions, GitLab CI, ArgoCD</div>
                        </div>
                        
                        <div class="skill-category">
                            <div class="skill-title">Infrastructure as Code</div>
                            <div class="skill-list">Terraform, CloudFormation, Ansible, Puppet</div>
                        </div>
                        
                        <div class="skill-category">
                            <div class="skill-title">Monitoring & Observability</div>
                            <div class="skill-list">Prometheus, Grafana, AlertManager, Fluent Bit, Grafana Loki, CloudWatch</div>
                        </div>
                        
                        <div class="skill-category">
                            <div class="skill-title">Security & Compliance</div>
                            <div class="skill-list">SonarQube, OWASP Dependency-Check, Trivy, Snyk, DevSecOps</div>
                        </div>
                        
                        <div class="skill-category">
                            <div class="skill-title">API Management</div>
                            <div class="skill-list">WSO2 API Manager, WSO2 Micro Integrator, REST APIs, OAuth2, Rate Limiting</div>
                        </div>
                        
                        <div class="skill-category">
                            <div class="skill-title">Programming & Scripting</div>
                            <div class="skill-list">Python, Java, Bash, SQL, YAML, JSON, ReactJS, JavaScript, HTML/CSS</div>
                        </div>
                        
                        <div class="skill-category">
                            <div class="skill-title">Database Technologies</div>
                            <div class="skill-list">MySQL, PostgreSQL, MongoDB, Redis, SQL Server</div>
                        </div>
                        
                        <div class="skill-category">
                            <div class="skill-title">Version Control & Collaboration</div>
                            <div class="skill-list">Git, GitHub, GitLab, JIRA, Confluence</div>
                        </div>
                    </div>
                </div>
                
                <div class="section">
                    <h2 class="section-title">Education</h2>
                    <div class="education-item">
                        <div class="degree">Master of Computer Application (MCA)</div>
                        <div class="university">
                            Madurai Kamaraj University<br>
                            Aug 2022 – May 2024 | CGPA: 7.2/10<br>
                            Madurai, Tamil Nadu
                        </div>
                    </div>
                </div>
                
                <div class="section">
                    <h2 class="section-title">Certifications</h2>
                    <div class="skill-list">
                        • AWS Solutions Architect (In Progress)<br>
                        • Kubernetes Administrator (In Progress)<br>
                        • Jenkins Certified Engineer<br>
                        • WSO2 API Manager Developer<br>
                        • Docker Associate (Planned)
                    </div>
                </div>
                
                <div class="section">
                    <h2 class="section-title">Key Achievements</h2>
                    <div class="skill-list">
                        • <strong>Cost Optimization:</strong> 40% AWS cost reduction<br>
                        • <strong>Security Enhancement:</strong> 95% fewer security incidents<br>
                        • <strong>Performance:</strong> 80% faster deployments<br>
                        • <strong>Reliability:</strong> 99.9% uptime maintained<br>
                        • <strong>Automation:</strong> 90% manual task elimination<br>
                        • <strong>Migration:</strong> 50+ APIs to cloud-native
                    </div>
                </div>
                
                <div class="section">
                    <h2 class="section-title">Live Portfolio</h2>
                    <div class="portfolio-links">
                        • <strong>ReactJS CI/CD:</strong> <a href="http://***********/">http://***********/</a><br>
                        • <strong>EKS Application:</strong> <a href="http://a663570b339364482a86a9e6f3445b69-1151892002.us-west-2.elb.amazonaws.com/">EKS App</a><br>
                        • <strong>AWS Pipeline:</strong> <a href="http://abb59c4b452064c768a9c0975c9ced80-1446271263.us-east-1.elb.amazonaws.com">AWS Native</a><br>
                        • <strong>Monitoring:</strong> Prometheus, Grafana, SonarQube dashboards available
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
