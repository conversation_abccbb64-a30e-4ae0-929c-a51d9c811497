<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> - Senior DevOps Engineer Resume</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', Arial, sans-serif;
            font-size: 9px;
            line-height: 1.3;
            color: #2c3e50;
            background: white;
            width: 210mm;
            height: 297mm;
            margin: 0 auto;
        }
        
        .resume {
            padding: 8mm 10mm;
            height: 100%;
        }
        
        .header {
            text-align: center;
            margin-bottom: 8px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 8px;
        }
        
        .name {
            font-size: 20px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 4px;
            letter-spacing: 1px;
        }
        
        .title {
            font-size: 12px;
            color: #3498db;
            margin-bottom: 6px;
            font-weight: 500;
        }
        
        .contact-info {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 15px;
            font-size: 9px;
            color: #34495e;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .two-column {
            display: grid;
            grid-template-columns: 70% 30%;
            gap: 12px;
        }
        
        .section {
            margin-bottom: 8px;
        }
        
        .section-title {
            font-size: 11px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 6px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-bottom: 1px solid #3498db;
            padding-bottom: 2px;
        }
        
        .summary {
            font-size: 9px;
            line-height: 1.4;
            margin-bottom: 8px;
            text-align: justify;
            color: #2c3e50;
        }
        
        .experience-item {
            margin-bottom: 8px;
            page-break-inside: avoid;
        }
        
        .job-title {
            font-size: 10px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 2px;
        }
        
        .company-date {
            font-size: 8px;
            color: #7f8c8d;
            margin-bottom: 4px;
            font-style: italic;
        }
        
        .star-item {
            margin-bottom: 6px;
            padding: 4px;
            background: #f8f9fa;
            border-left: 3px solid #3498db;
            border-radius: 0 3px 3px 0;
        }
        
        .star-title {
            font-size: 9px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 2px;
        }
        
        .star-content {
            font-size: 8px;
            line-height: 1.3;
            color: #34495e;
        }
        
        .star-result {
            font-size: 8px;
            font-weight: 600;
            color: #27ae60;
            margin-top: 2px;
        }
        
        .tech-stack {
            font-size: 7px;
            color: #7f8c8d;
            margin-bottom: 3px;
            font-style: italic;
        }
        
        .skills-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 4px;
        }
        
        .skill-category {
            margin-bottom: 4px;
        }
        
        .skill-title {
            font-size: 8px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 2px;
        }
        
        .skill-list {
            font-size: 7px;
            line-height: 1.3;
            color: #555;
        }
        
        .education-item {
            margin-bottom: 4px;
        }
        
        .degree {
            font-size: 8px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 1px;
        }
        
        .university {
            font-size: 7px;
            color: #7f8c8d;
            line-height: 1.3;
        }
        
        .highlight {
            font-weight: 600;
            color: #e74c3c;
        }
        
        .portfolio-links {
            font-size: 7px;
            line-height: 1.4;
        }
        
        .portfolio-links a {
            color: #3498db;
            text-decoration: none;
        }
        
        @media print {
            body {
                width: 210mm;
                height: 297mm;
                margin: 0;
                padding: 0;
            }
            
            .resume {
                padding: 8mm 10mm;
                height: 100%;
            }
            
            @page {
                size: A4;
                margin: 0;
            }
        }
    </style>
</head>
<body>
    <div class="resume">
        <div class="header">
            <h1 class="name">DILIP KUMAR</h1>
            <p class="title">Senior DevOps Engineer | Cloud Infrastructure Specialist | API Management Expert</p>
            <div class="contact-info">
                <div class="contact-item">📧 <EMAIL></div>
                <div class="contact-item">📱 +************</div>
                <div class="contact-item">🔗 github.com/Dilip-Devopos</div>
                <div class="contact-item">💼 linkedin.com/in/dilipkumarselvam</div>
                <div class="contact-item">📍 Bangalore, India</div>
            </div>
        </div>
        
        <div class="two-column">
            <div class="left-column">
                <div class="section">
                    <h2 class="section-title">Professional Summary</h2>
                    <p class="summary">
                        Accomplished Senior DevOps Engineer with <span class="highlight">4+ years</span> of expertise in enterprise software development and cloud infrastructure automation. Specialized in designing end-to-end CI/CD pipelines, containerized deployments, and API management solutions. Successfully delivered <span class="highlight">20+ production applications</span> with <span class="highlight">99.9% uptime</span>, reduced deployment time by <span class="highlight">80%</span>, and implemented comprehensive monitoring ecosystems serving <span class="highlight">10,000+ users</span>.
                    </p>
                </div>
                
                <div class="section">
                    <h2 class="section-title">Professional Experience</h2>
                    
                    <div class="experience-item">
                        <div class="job-title">Software Developer - DevOps Engineer</div>
                        <div class="company-date">ZeOmega Infotech, Bangalore | Nov 2022 – Oct 2024</div>
                        
                        <div class="star-item">
                            <div class="star-title">Enterprise WSO2 API Management Migration & DevOps Implementation</div>
                            <div class="tech-stack">WSO2 API Manager • WSO2 Micro Integrator • Jenkins • Docker • Kubernetes • Puppet • Java</div>
                            <div class="star-content">
                                <strong>Situation:</strong> Legacy WSO2 API Manager and Micro Integrator running on Windows servers with manual deployment processes, causing security vulnerabilities and operational inefficiencies.<br>
                                <strong>Task:</strong> Migrate 50+ enterprise APIs to cloud-native Kubernetes architecture while implementing automated CI/CD pipelines and security scanning.<br>
                                <strong>Action:</strong> Engineered robust CI/CD pipeline using Jenkins with Dependency Check tool for JAR file vulnerability scanning. Designed automated workflows using Puppet for environment orchestration and WSO2 deployment. Spearheaded complete migration from Windows to Kubernetes platform with zero downtime. Implemented comprehensive API security framework with rate limiting, OAuth2 authentication flows, and key management.<br>
                            </div>
                            <div class="star-result">Result: Successfully migrated 50+ APIs with 60% faster deployments, 99.8% availability, and 65% reduction in manual setup time.</div>
                        </div>
                    </div>
                    
                    <div class="experience-item">
                        <div class="job-title">Senior DevOps Engineer (Freelance Projects)</div>
                        <div class="company-date">Independent Projects | 2024 - Present</div>
                        
                        <div class="star-item">
                            <div class="star-title">Enterprise ReactJS CI/CD Pipeline with Advanced Security</div>
                            <div class="tech-stack">Jenkins • Docker • AWS EC2 • SonarQube • OWASP • Trivy • Prometheus • Grafana</div>
                            <div class="star-content">
                                <strong>Situation:</strong> ReactJS application requiring secure, automated deployment pipeline with comprehensive monitoring for 5,000+ concurrent users.<br>
                                <strong>Task:</strong> Architect enterprise-grade DevSecOps pipeline with multi-branch strategy, security scanning, and real-time monitoring.<br>
                                <strong>Action:</strong> Crafted multi-branch CI/CD strategy using Jenkins Multibranch Pipelines for development/production environments. Embedded advanced security scanning (OWASP, Trivy) and integrated SonarQube for code quality analysis. Deployed comprehensive monitoring solution with Prometheus, Grafana, and AlertManager. Configured Docker Hub integration with segregated repositories.<br>
                            </div>
                            <div class="star-result">Result: Achieved 99.9% uptime, 95% reduction in security incidents, and accelerated deployment from 2 hours to 15 minutes.</div>
                        </div>
                        
                        <div class="star-item">
                            <div class="star-title">Kubernetes-Native Application Deployment on AWS EKS</div>
                            <div class="tech-stack">AWS EKS • Terraform • Kubernetes • Jenkins • Prometheus • Grafana • Fluent Bit</div>
                            <div class="star-content">
                                <strong>Situation:</strong> Need for scalable, cloud-native infrastructure supporting containerized applications with comprehensive observability.<br>
                                <strong>Task:</strong> Design and provision AWS infrastructure using Infrastructure as Code with automated CI/CD and monitoring.<br>
                                <strong>Action:</strong> Designed AWS infrastructure using Terraform (VPC, subnets, security groups, EKS cluster) with GitOps workflow. Executed end-to-end CI/CD with automated Docker builds and Kubernetes deployments via Jenkins and Helm charts. Instituted comprehensive observability using Prometheus, Grafana, and Fluent Bit for centralized logging.<br>
                            </div>
                            <div class="star-result">Result: Realized 40% cost optimization through right-sizing and auto-scaling, managed 10+ microservices with 35% resource cost reduction.</div>
                        </div>
                        
                        <div class="star-item">
                            <div class="star-title">AWS-Native CI/CD Pipeline with Advanced Automation</div>
                            <div class="tech-stack">AWS CodePipeline • CodeBuild • CodeDeploy • ECR • EKS • CloudWatch • SNS</div>
                            <div class="star-content">
                                <strong>Situation:</strong> Requirement for AWS-native CI/CD solution with automated vulnerability scanning and notification systems.<br>
                                <strong>Task:</strong> Construct fully automated pipeline using AWS services with rollback capabilities and zero-downtime deployments.<br>
                                <strong>Action:</strong> Constructed AWS-native CI/CD solution utilizing CodePipeline, CodeBuild, and CodeDeploy. Devised automated Docker image management with ECR integration and sophisticated tagging strategies. Pioneered vulnerability scanning pipeline leveraging Trivy with automated reporting and SNS notification systems.<br>
                            </div>
                            <div class="star-result">Result: Attained 90% reduction in manual deployment tasks, processed 100+ automated deployments with 99.5% success rate.</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="right-column">
                <div class="section">
                    <h2 class="section-title">Technical Skills</h2>
                    <div class="skills-grid">
                        <div class="skill-category">
                            <div class="skill-title">Cloud Platforms</div>
                            <div class="skill-list">AWS (EC2, EKS, ECR, CodePipeline, CodeBuild, CodeDeploy, CloudWatch, SNS, VPC, IAM, CloudFormation)</div>
                        </div>
                        
                        <div class="skill-category">
                            <div class="skill-title">Containers & Orchestration</div>
                            <div class="skill-list">Docker, Kubernetes, Helm, Rancher, Docker Compose</div>
                        </div>
                        
                        <div class="skill-category">
                            <div class="skill-title">CI/CD & DevOps</div>
                            <div class="skill-list">Jenkins, AWS CodePipeline, GitHub Actions, GitLab CI, ArgoCD</div>
                        </div>
                        
                        <div class="skill-category">
                            <div class="skill-title">Infrastructure as Code</div>
                            <div class="skill-list">Terraform, CloudFormation, Ansible, Puppet</div>
                        </div>
                        
                        <div class="skill-category">
                            <div class="skill-title">Monitoring & Observability</div>
                            <div class="skill-list">Prometheus, Grafana, AlertManager, Fluent Bit, Grafana Loki, CloudWatch</div>
                        </div>
                        
                        <div class="skill-category">
                            <div class="skill-title">Security & Compliance</div>
                            <div class="skill-list">SonarQube, OWASP Dependency-Check, Trivy, Snyk, DevSecOps</div>
                        </div>
                        
                        <div class="skill-category">
                            <div class="skill-title">API Management</div>
                            <div class="skill-list">WSO2 API Manager, WSO2 Micro Integrator, REST APIs, OAuth2, Rate Limiting</div>
                        </div>
                        
                        <div class="skill-category">
                            <div class="skill-title">Programming & Scripting</div>
                            <div class="skill-list">Python, Java, Bash, SQL, YAML, JSON, ReactJS, JavaScript, HTML/CSS</div>
                        </div>
                        
                        <div class="skill-category">
                            <div class="skill-title">Database Technologies</div>
                            <div class="skill-list">MySQL, PostgreSQL, MongoDB, Redis, SQL Server</div>
                        </div>
                        
                        <div class="skill-category">
                            <div class="skill-title">Version Control & Collaboration</div>
                            <div class="skill-list">Git, GitHub, GitLab, JIRA, Confluence</div>
                        </div>
                    </div>
                </div>
                
                <div class="section">
                    <h2 class="section-title">Education</h2>
                    <div class="education-item">
                        <div class="degree">Master of Computer Application (MCA)</div>
                        <div class="university">
                            Madurai Kamaraj University<br>
                            Aug 2022 – May 2024 | CGPA: 7.2/10<br>
                            Madurai, Tamil Nadu
                        </div>
                    </div>
                </div>
                
                <div class="section">
                    <h2 class="section-title">Certifications</h2>
                    <div class="skill-list">
                        • AWS Solutions Architect (In Progress)<br>
                        • Kubernetes Administrator (In Progress)<br>
                        • Jenkins Certified Engineer<br>
                        • WSO2 API Manager Developer<br>
                        • Docker Associate (Planned)
                    </div>
                </div>
                
                <div class="section">
                    <h2 class="section-title">Key Achievements</h2>
                    <div class="skill-list">
                        • <strong>Cost Optimization:</strong> 40% AWS cost reduction<br>
                        • <strong>Security Enhancement:</strong> 95% fewer security incidents<br>
                        • <strong>Performance:</strong> 80% faster deployments<br>
                        • <strong>Reliability:</strong> 99.9% uptime maintained<br>
                        • <strong>Automation:</strong> 90% manual task elimination<br>
                        • <strong>Migration:</strong> 50+ APIs to cloud-native
                    </div>
                </div>
                
                <div class="section">
                    <h2 class="section-title">Live Portfolio</h2>
                    <div class="portfolio-links">
                        • <strong>ReactJS CI/CD:</strong> <a href="http://***********/">http://***********/</a><br>
                        • <strong>EKS Application:</strong> <a href="http://a663570b339364482a86a9e6f3445b69-1151892002.us-west-2.elb.amazonaws.com/">EKS App</a><br>
                        • <strong>AWS Pipeline:</strong> <a href="http://abb59c4b452064c768a9c0975c9ced80-1446271263.us-east-1.elb.amazonaws.com">AWS Native</a><br>
                        • <strong>Monitoring:</strong> Prometheus, Grafana, SonarQube dashboards available
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
